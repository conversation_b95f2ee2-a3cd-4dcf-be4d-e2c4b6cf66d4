[{"name": "Traversaal", "pluginKey": "traversaal_search", "description": "Traversaal is a robust search API tailored for LLM Agents. Get an API key here: https://api.traversaal.ai", "icon": "https://traversaal.ai/favicon.ico", "authConfig": [{"authField": "TRAVERSAAL_API_KEY", "label": "Traversaal API Key", "description": "Get your API key here: <a href=\"https://api.traversaal.ai\" target=\"_blank\">https://api.traversaal.ai</a>"}]}, {"name": "Google", "pluginKey": "google", "description": "Use Google Search to find information about the weather, news, sports, and more.", "icon": "https://i.imgur.com/SMmVkNB.png", "authConfig": [{"authField": "GOOGLE_CSE_ID", "label": "Google CSE ID", "description": "This is your Google Custom Search Engine ID. For instructions on how to obtain this, see <a href='https://github.com/danny-a<PERSON>/LibreChat/blob/main/docs/features/plugins/google_search.md'>Our Docs</a>."}, {"authField": "GOOGLE_SEARCH_API_KEY", "label": "Google API Key", "description": "This is your Google Custom Search API Key. For instructions on how to obtain this, see <a href='https://github.com/danny-a<PERSON>/LibreChat/blob/main/docs/features/plugins/google_search.md'>Our Docs</a>."}]}, {"name": "YouTube", "pluginKey": "youtube", "toolkit": true, "description": "Get YouTube video information, retrieve comments, analyze transcripts and search for videos.", "icon": "https://www.youtube.com/s/desktop/7449ebf7/img/favicon_144x144.png", "authConfig": [{"authField": "YOUTUBE_API_KEY", "label": "YouTube API Key", "description": "Your YouTube Data API v3 key."}]}, {"name": "OpenAI Image Tools", "pluginKey": "image_gen_oai", "toolkit": true, "description": "Image Generation and Editing using OpenAI's latest state-of-the-art models", "icon": "assets/image_gen_oai.png", "authConfig": [{"authField": "IMAGE_GEN_OAI_API_KEY", "label": "OpenAI Image Tools API Key", "description": "Your OpenAI API Key for Image Generation and Editing"}]}, {"name": "<PERSON><PERSON>", "pluginKey": "wolfram", "description": "Access computation, math, curated knowledge & real-time data through Wolfram|Alpha and Wolfram Language.", "icon": "https://www.wolframcdn.com/images/icons/Wolfram.png", "authConfig": [{"authField": "WOLFRAM_APP_ID", "label": "Wolfram App ID", "description": "An AppID must be supplied in all calls to the Wolfram|Alpha API. You can get one by registering at <a href='http://products.wolframalpha.com/api/'>Wolfram|Alpha</a> and going to the <a href='https://developer.wolframalpha.com/portal/myapps/'>Developer Portal</a>."}]}, {"name": "Browser", "pluginKey": "web-browser", "description": "Scrape and summarize webpage data", "icon": "assets/web-browser.svg", "authConfig": [{"authField": "OPENAI_API_KEY", "label": "OpenAI API Key", "description": "Browser makes use of OpenAI embeddings"}]}, {"name": "<PERSON><PERSON><PERSON>", "pluginKey": "serp<PERSON>i", "description": "SerpApi is a real-time API to access search engine results.", "icon": "https://i.imgur.com/5yQHUz4.png", "authConfig": [{"authField": "SERPAPI_API_KEY", "label": "Serpapi Private API Key", "description": "Private Key for Serpapi. Register at <a href='https://serpapi.com/'>Serpapi</a> to obtain a private key."}]}, {"name": "DALL-E-3", "pluginKey": "dalle", "description": "[DALL-E-3] Create realistic images and art from a description in natural language", "icon": "https://i.imgur.com/u2TzXzH.png", "authConfig": [{"authField": "DALLE3_API_KEY||DALLE_API_KEY", "label": "OpenAI API Key", "description": "You can use DALL-E with your API Key from OpenAI."}]}, {"name": "<PERSON><PERSON>", "pluginKey": "tavily_search_results_json", "description": "Tavily Search is a robust search API tailored for LLM Agents. It seamlessly integrates with diverse data sources to ensure a superior, relevant search experience.", "icon": "https://tavily.com/favicon.ico", "authConfig": [{"authField": "TAVILY_API_KEY", "label": "Tavily API Key", "description": "Get your API key here: https://app.tavily.com/"}]}, {"name": "Calculator", "pluginKey": "calculator", "description": "Perform simple and complex mathematical calculations.", "icon": "https://i.imgur.com/RHsSG5h.png", "authConfig": []}, {"name": "Stable Diffusion", "pluginKey": "stable-diffusion", "description": "Generate photo-realistic images given any text input.", "icon": "https://i.imgur.com/Yr466dp.png", "authConfig": [{"authField": "SD_WEBUI_URL", "label": "Your Stable Diffusion WebUI API URL", "description": "You need to provide the URL of your Stable Diffusion WebUI API. For instructions on how to obtain this, see <a href='url'>Our Docs</a>."}]}, {"name": "Azure AI Search", "pluginKey": "azure-ai-search", "description": "Use Azure AI Search to find information", "icon": "https://i.imgur.com/E7crPze.png", "authConfig": [{"authField": "AZURE_AI_SEARCH_SERVICE_ENDPOINT", "label": "Azure AI Search Endpoint", "description": "You need to provide your Endpoint for Azure AI Search."}, {"authField": "AZURE_AI_SEARCH_INDEX_NAME", "label": "Azure AI Search Index Name", "description": "You need to provide your Index Name for Azure AI Search."}, {"authField": "AZURE_AI_SEARCH_API_KEY", "label": "Azure AI Search API Key", "description": "You need to provide your API Key for Azure AI Search."}]}, {"name": "OpenWeather", "pluginKey": "open_weather", "description": "Get weather forecasts and historical data from the OpenWeather API", "icon": "assets/openweather.png", "authConfig": [{"authField": "OPENWEATHER_API_KEY", "label": "OpenWeather API Key", "description": "Sign up at <a href=\"https://home.openweathermap.org/users/sign_up\" target=\"_blank\">OpenWeather</a>, then get your key at <a href=\"https://home.openweathermap.org/api_keys\" target=\"_blank\">API keys</a>."}]}, {"name": "Flux", "pluginKey": "flux", "description": "Generate images using text with the Flux API.", "icon": "https://blackforestlabs.ai/wp-content/uploads/2024/07/bfl_logo_retraced_blk.png", "isAuthRequired": "true", "authConfig": [{"authField": "FLUX_API_KEY", "label": "Your Flux API Key", "description": "Provide your Flux API key from your user profile."}]}]